# Crimson Advanced Reconciliation Tool

A comprehensive reconciliation tool for political fundraising CRM that helps federal campaigns, PACs, and nonprofits with FEC compliance and treasury management.

## Features

- **Advanced Transaction Reconciliation** - Match Crimson CRM transactions with bank activity
- **AI-Powered Suggestions** - Intelligent transaction matching with confidence scoring
- **FEC Compliance** - Built-in compliance features for federal campaign finance
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices
- **Real-time Dashboard** - Live reconciliation status and cash on hand tracking
- **Comprehensive Reporting** - Detailed reconciliation reports and history

## Technology Stack

- **Frontend**: React 19 with TypeScript
- **Build Tool**: Vite 6
- **Styling**: Tailwind CSS
- **AI Integration**: Google Gemini API
- **Deployment**: Render

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/sofiaborden/crimson-advanced-reconciliation-tool---with-changes.git
cd crimson-advanced-reconciliation-tool---with-changes
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

### Environment Variables

For AI features, set up your Gemini API key:

```bash
GEMINI_API_KEY=your_api_key_here
```

## Deployment

This application is configured for deployment on Render as a static site.

### Build Commands

- **Build Command**: `./render-build.sh`
- **Publish Directory**: `dist`

## License

Private - Crimson CRM Integration Tool
