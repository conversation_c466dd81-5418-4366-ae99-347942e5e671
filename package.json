{"name": "crimson-advanced-reconciliation-tool---with-changes", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-types": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "render-build": "npm ci && npm run build"}, "dependencies": {"@google/genai": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^6.2.0"}}