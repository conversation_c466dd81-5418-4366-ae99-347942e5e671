{"name": "crimson-advanced-reconciliation-tool---with-changes", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-types": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react-dom": "^19.1.0", "react": "^19.1.0", "@google/genai": "^1.9.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.3.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}