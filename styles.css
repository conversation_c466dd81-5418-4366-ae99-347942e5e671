/* Tailwind CSS Base Styles */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before,
::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
  font-family: 'Inter', sans-serif;
}

/* Reset and base styles */
hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, "Consolas", "Liberation Mono", "Menlo", monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button,
select {
  text-transform: none;
}

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button,
[role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}

[hidden] {
  display: none;
}

/* Custom Tailwind-like utilities */
.bg-slate-50 { background-color: #f8fafc; }
.bg-white { background-color: #ffffff; }
.bg-slate-100 { background-color: #f1f5f9; }
.bg-slate-200 { background-color: #e2e8f0; }
.bg-slate-800 { background-color: #1e293b; }
.bg-sky-50 { background-color: #f0f9ff; }
.bg-sky-100 { background-color: #e0f2fe; }
.bg-sky-600 { background-color: #0284c7; }
.bg-sky-700 { background-color: #0369a1; }
.bg-emerald-50 { background-color: #ecfdf5; }
.bg-emerald-100 { background-color: #d1fae5; }
.bg-emerald-600 { background-color: #059669; }
.bg-emerald-700 { background-color: #047857; }
.bg-rose-50 { background-color: #fff1f2; }
.bg-rose-100 { background-color: #ffe4e6; }
.bg-rose-600 { background-color: #e11d48; }
.bg-rose-700 { background-color: #be123c; }
.bg-orange-50 { background-color: #fff7ed; }
.bg-orange-100 { background-color: #ffedd5; }
.bg-orange-600 { background-color: #ea580c; }
.bg-orange-700 { background-color: #c2410c; }
.bg-indigo-600 { background-color: #4f46e5; }
.bg-indigo-700 { background-color: #4338ca; }

.text-white { color: #ffffff; }
.text-slate-600 { color: #475569; }
.text-slate-700 { color: #334155; }
.text-slate-800 { color: #1e293b; }
.text-slate-400 { color: #94a3b8; }
.text-sky-600 { color: #0284c7; }
.text-sky-700 { color: #0369a1; }
.text-emerald-600 { color: #059669; }
.text-emerald-700 { color: #047857; }
.text-rose-600 { color: #e11d48; }
.text-rose-700 { color: #be123c; }
.text-orange-600 { color: #ea580c; }
.text-orange-700 { color: #c2410c; }

.border { border-width: 1px; }
.border-slate-200 { border-color: #e2e8f0; }
.border-sky-200 { border-color: #bae6fd; }
.border-sky-300 { border-color: #7dd3fc; }
.border-emerald-200 { border-color: #a7f3d0; }
.border-emerald-300 { border-color: #6ee7b7; }
.border-rose-200 { border-color: #fecdd3; }
.border-rose-300 { border-color: #fda4af; }
.border-orange-200 { border-color: #fed7aa; }
.border-orange-300 { border-color: #fdba74; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }

.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-7 { width: 1.75rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-7 { height: 1.75rem; }
.h-full { height: 100%; }
.min-h-0 { min-height: 0; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }

.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }

.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }

.hover\:bg-sky-100:hover { background-color: #e0f2fe; }
.hover\:bg-sky-700:hover { background-color: #0369a1; }
.hover\:bg-emerald-100:hover { background-color: #d1fae5; }
.hover\:bg-emerald-700:hover { background-color: #047857; }
.hover\:bg-rose-100:hover { background-color: #ffe4e6; }
.hover\:bg-rose-700:hover { background-color: #be123c; }
.hover\:bg-orange-100:hover { background-color: #ffedd5; }
.hover\:bg-orange-700:hover { background-color: #c2410c; }
.hover\:bg-indigo-700:hover { background-color: #4338ca; }

.hover\:text-sky-600:hover { color: #0284c7; }
.hover\:text-sky-700:hover { color: #0369a1; }
.hover\:text-emerald-600:hover { color: #059669; }
.hover\:text-emerald-700:hover { color: #047857; }
.hover\:text-rose-600:hover { color: #e11d48; }
.hover\:text-rose-700:hover { color: #be123c; }
.hover\:text-orange-600:hover { color: #ea580c; }
.hover\:text-orange-700:hover { color: #c2410c; }

.hover\:border-sky-300:hover { border-color: #7dd3fc; }
.hover\:border-emerald-300:hover { border-color: #6ee7b7; }
.hover\:border-rose-300:hover { border-color: #fda4af; }
.hover\:border-orange-300:hover { border-color: #fdba74; }

.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-sky-500:focus { --tw-ring-color: #0ea5e9; }
.focus\:ring-emerald-500:focus { --tw-ring-color: #10b981; }
.focus\:ring-rose-500:focus { --tw-ring-color: #f43f5e; }
.focus\:ring-orange-500:focus { --tw-ring-color: #f97316; }
.focus\:ring-offset-1:focus { --tw-ring-offset-width: 1px; }

.whitespace-nowrap { white-space: nowrap; }

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:w-7 { width: 1.75rem; }
  .sm\:h-7 { height: 1.75rem; }
  .sm\:h-4 { height: 1rem; }
  .sm\:w-4 { width: 1rem; }
  .sm\:px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:flex-row { flex-direction: row; }
  .lg\:items-center { align-items: center; }
}
