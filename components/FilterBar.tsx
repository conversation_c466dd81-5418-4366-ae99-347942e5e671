import React from 'react';
import { MagnifyingGlassIcon, XMarkIcon } from './Icons';
import { ReconciliationStatus, FilterOptions } from '../types';
import { MultiSelect } from './MultiSelect';

interface FilterBarProps {
    statusFilter: ReconciliationStatus;
    onStatusFilterChange: (status: ReconciliationStatus) => void;
    isCrimson: boolean;
    filters: FilterOptions;
    onFiltersChange: (filters: FilterOptions) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({ statusFilter, onStatusFilterChange, isCrimson, filters, onFiltersChange }) => {
    const updateFilter = (key: keyof FilterOptions, value: any) => {
        onFiltersChange({ ...filters, [key]: value });
    };

    const clearAllFilters = () => {
        onFiltersChange({
            statusFilter: ReconciliationStatus.Unreconciled,
            searchText: '',
            dateRange: { start: '', end: '' },
            amountRange: { min: null, max: null },
            fundCode: ['All'],
            accountCode: ['All'],
            paymentType: 'All',
            lineNumber: 'All',
            showAdvanced: false,
        });
        onStatusFilterChange(ReconciliationStatus.Unreconciled);
    };

    const isFundCodeFiltered = Array.isArray(filters.fundCode)
        ? !filters.fundCode.includes('All') && filters.fundCode.length > 0
        : filters.fundCode !== 'All';

    const isAccountCodeFiltered = Array.isArray(filters.accountCode)
        ? !filters.accountCode.includes('All') && filters.accountCode.length > 0
        : filters.accountCode !== 'All';

    const hasActiveFilters = filters.searchText ||
        filters.dateRange.start ||
        filters.dateRange.end ||
        filters.amountRange.min !== null ||
        filters.amountRange.max !== null ||
        isFundCodeFiltered ||
        isAccountCodeFiltered ||
        filters.paymentType !== 'All' ||
        (filters.lineNumber && filters.lineNumber !== 'All');

    const activeFilterCount = [
        filters.searchText,
        filters.dateRange.start || filters.dateRange.end,
        filters.amountRange.min !== null || filters.amountRange.max !== null,
        isFundCodeFiltered,
        isAccountCodeFiltered,
        filters.paymentType !== 'All',
        filters.lineNumber && filters.lineNumber !== 'All'
    ].filter(Boolean).length;

    return (
        <div className="bg-slate-50 p-3 rounded-lg border border-slate-200/80">
            {/* Compact Top Row */}
            <div className="flex flex-wrap items-center gap-2 mb-3">
                {/* Search */}
                <div className="flex-1 min-w-[200px] relative">
                    <MagnifyingGlassIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Search..."
                        value={filters.searchText}
                        onChange={(e) => updateFilter('searchText', e.target.value)}
                        className="w-full pl-8 pr-8 py-1.5 border border-slate-300 rounded text-sm focus:ring-sky-500 focus:border-sky-500"
                    />
                    {filters.searchText && (
                        <button
                            onClick={() => updateFilter('searchText', '')}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                        >
                            <XMarkIcon className="w-4 h-4" />
                        </button>
                    )}
                </div>

                {/* Status Toggle */}
                <div className="flex rounded border border-slate-300 overflow-hidden">
                    <button
                        onClick={() => onStatusFilterChange(ReconciliationStatus.Unreconciled)}
                        className={`px-3 py-1.5 text-xs font-medium transition-colors ${
                            statusFilter === ReconciliationStatus.Unreconciled 
                                ? 'bg-sky-600 text-white' 
                                : 'bg-white text-slate-700 hover:bg-slate-100'
                        }`}
                    >
                        Unreconciled
                    </button>
                    <button
                        onClick={() => onStatusFilterChange(ReconciliationStatus.All)}
                        className={`px-3 py-1.5 text-xs font-medium border-l border-slate-300 transition-colors ${
                            statusFilter === ReconciliationStatus.All 
                                ? 'bg-sky-600 text-white' 
                                : 'bg-white text-slate-700 hover:bg-slate-100'
                        }`}
                    >
                        All
                    </button>
                </div>

                {/* Quick Filters */}
                <button
                    onClick={() => updateFilter('showAdvanced', !filters.showAdvanced)}
                    className={`px-3 py-1.5 text-xs font-medium rounded transition-colors ${
                        filters.showAdvanced 
                            ? 'bg-sky-100 text-sky-800' 
                            : 'bg-white text-slate-600 hover:bg-slate-100'
                    } border border-slate-300`}
                >
                    Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
                </button>

                {hasActiveFilters && (
                    <button
                        onClick={clearAllFilters}
                        className="px-2 py-1.5 text-xs font-medium text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded transition-colors"
                        title="Clear all filters"
                    >
                        <XMarkIcon className="w-4 h-4" />
                    </button>
                )}
            </div>

            {/* Expandable Advanced Filters */}
            {filters.showAdvanced && (
                <div className="border-t border-slate-200 pt-3 space-y-3">
                    {/* Date and Amount Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <div>
                            <label className="block text-xs font-medium text-slate-600 mb-1">Date Range</label>
                            <div className="flex gap-1">
                                <input
                                    type="date"
                                    value={filters.dateRange.start}
                                    onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, start: e.target.value })}
                                    className="flex-1 p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500"
                                />
                                <input
                                    type="date"
                                    value={filters.dateRange.end}
                                    onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, end: e.target.value })}
                                    className="flex-1 p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-xs font-medium text-slate-600 mb-1">Amount Range</label>
                            <div className="flex gap-1">
                                <input
                                    type="number"
                                    placeholder="Min"
                                    value={filters.amountRange.min || ''}
                                    onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, min: e.target.value ? parseFloat(e.target.value) : null })}
                                    className="flex-1 p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500"
                                />
                                <input
                                    type="number"
                                    placeholder="Max"
                                    value={filters.amountRange.max || ''}
                                    onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, max: e.target.value ? parseFloat(e.target.value) : null })}
                                    className="flex-1 p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Category Filters Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <MultiSelect
                            label={isCrimson ? 'Fund Code' : 'Transaction Type'}
                            options={[
                                { value: 'All', label: 'All' },
                                ...(isCrimson ? [
                                    { value: 'P2026', label: 'P2026' },
                                    { value: 'G2026', label: 'G2026' },
                                    { value: 'PAC', label: 'PAC' },
                                    { value: 'JFC', label: 'JFC' }
                                ] : [
                                    { value: 'Credit', label: 'Credit Only' },
                                    { value: 'Debit', label: 'Debit Only' }
                                ])
                            ]}
                            selectedValues={Array.isArray(filters.fundCode) ? filters.fundCode : [filters.fundCode]}
                            onChange={(values) => updateFilter('fundCode', values)}
                        />
                        <div>
                            <label className="block text-xs font-medium text-slate-600 mb-1">
                                {isCrimson ? 'Payment Type' : 'Description'}
                            </label>
                            {isCrimson ? (
                                <select 
                                    className="w-full p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500 bg-white"
                                    value={filters.paymentType}
                                    onChange={(e) => updateFilter('paymentType', e.target.value)}
                                >
                                   <option value="All">All</option>
                                   <option value="CH">CH - Check</option>
                                   <option value="CC">CC - Credit Card</option>
                                   <option value="JF">JF - Joint Fundraising</option>
                                   <option value="IK">IK - In-Kind</option>
                                   <option value="OT">OT - Other</option>
                                   <option value="WR">WR - WinRed</option>
                                   <option value="AN">AN - ActBlue</option>
                                </select>
                             ) : (
                                <input 
                                    type="text" 
                                    placeholder="Contains text..." 
                                    className="w-full p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500"
                                    value={filters.paymentType === 'All' ? '' : filters.paymentType}
                                    onChange={(e) => updateFilter('paymentType', e.target.value || 'All')}
                                />
                             )}
                        </div>
                    </div>

                    {/* Additional Filters */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <MultiSelect
                            label="Account Code"
                            options={[
                                { value: 'All', label: 'All' },
                                ...(isCrimson ? [
                                    { value: 'Operating P2026', label: 'Operating P2026' },
                                    { value: 'Operating G2026', label: 'Operating G2026' },
                                    { value: 'Operating PAC', label: 'Operating PAC' },
                                    { value: 'Savings 2026', label: 'Savings 2026' }
                                ] : [
                                    { value: 'Checking', label: 'Checking Account' },
                                    { value: 'Savings', label: 'Savings Account' },
                                    { value: 'Money Market', label: 'Money Market' },
                                    { value: 'Credit Card', label: 'Credit Card' }
                                ])
                            ]}
                            selectedValues={Array.isArray(filters.accountCode) ? filters.accountCode : [filters.accountCode]}
                            onChange={(values) => updateFilter('accountCode', values)}
                        />
                        {isCrimson && !Array.isArray(filters.fundCode) ? filters.fundCode !== 'All' : (Array.isArray(filters.fundCode) && !filters.fundCode.includes('All')) && (
                            <div>
                                <label className="block text-xs font-medium text-slate-600 mb-1">Line Number</label>
                                <select
                                    className="w-full p-1.5 border border-slate-300 rounded text-xs focus:ring-sky-500 focus:border-sky-500 bg-white"
                                    value={filters.lineNumber || 'All'}
                                    onChange={(e) => updateFilter('lineNumber', e.target.value)}
                                >
                                    <option value="All">All</option>
                                    <option value="SA11A">SA11A - Contributions</option>
                                    <option value="SA17">SA17 - Refunds</option>
                                    <option value="SB21B">SB21B - Operating Expenditures</option>
                                    <option value="SB23">SB23 - Contributions to Candidates</option>
                                    <option value="SB29">SB29 - JFC Transfers Out</option>
                                </select>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Active Filters Pills */}
            {hasActiveFilters && (
                <div className="mt-3 pt-2 border-t border-slate-200">
                    <div className="flex flex-wrap gap-1 items-center">
                        <span className="text-xs text-slate-500 mr-1">Active:</span>
                        {filters.searchText && (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-sky-100 text-sky-800 text-xs rounded">
                                "{filters.searchText.substring(0, 15)}{filters.searchText.length > 15 ? '...' : ''}"
                                <button onClick={() => updateFilter('searchText', '')} className="hover:text-sky-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {(filters.dateRange.start || filters.dateRange.end) && (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-emerald-100 text-emerald-800 text-xs rounded">
                                Date range
                                <button onClick={() => updateFilter('dateRange', { start: '', end: '' })} className="hover:text-emerald-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {(filters.amountRange.min !== null || filters.amountRange.max !== null) && (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded">
                                Amount range
                                <button onClick={() => updateFilter('amountRange', { min: null, max: null })} className="hover:text-purple-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {isFundCodeFiltered && (
                            <>
                                {(Array.isArray(filters.fundCode) ? filters.fundCode : [filters.fundCode])
                                    .filter(code => code !== 'All')
                                    .map(code => (
                                        <span key={`fund-${code}`} className="inline-flex items-center gap-1 px-2 py-0.5 bg-orange-100 text-orange-800 text-xs rounded">
                                            Fund: {code}
                                            <button
                                                onClick={() => {
                                                    const currentCodes = Array.isArray(filters.fundCode) ? filters.fundCode : [filters.fundCode];
                                                    const newCodes = currentCodes.filter(c => c !== code);
                                                    updateFilter('fundCode', newCodes.length === 0 ? ['All'] : newCodes);
                                                }}
                                                className="hover:text-orange-600"
                                            >
                                                <XMarkIcon className="w-3 h-3" />
                                            </button>
                                        </span>
                                    ))
                                }
                            </>
                        )}
                        {filters.paymentType !== 'All' && (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-indigo-100 text-indigo-800 text-xs rounded">
                                {filters.paymentType}
                                <button onClick={() => updateFilter('paymentType', 'All')} className="hover:text-indigo-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {isAccountCodeFiltered && (
                            <>
                                {(Array.isArray(filters.accountCode) ? filters.accountCode : [filters.accountCode])
                                    .filter(code => code !== 'All')
                                    .map(code => (
                                        <span key={`account-${code}`} className="inline-flex items-center gap-1 px-2 py-0.5 bg-rose-100 text-rose-800 text-xs rounded">
                                            Account: {code}
                                            <button
                                                onClick={() => {
                                                    const currentCodes = Array.isArray(filters.accountCode) ? filters.accountCode : [filters.accountCode];
                                                    const newCodes = currentCodes.filter(c => c !== code);
                                                    updateFilter('accountCode', newCodes.length === 0 ? ['All'] : newCodes);
                                                }}
                                                className="hover:text-rose-600"
                                            >
                                                <XMarkIcon className="w-3 h-3" />
                                            </button>
                                        </span>
                                    ))
                                }
                            </>
                        )}
                        {filters.lineNumber && filters.lineNumber !== 'All' && (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-teal-100 text-teal-800 text-xs rounded">
                                {filters.lineNumber}
                                <button onClick={() => updateFilter('lineNumber', 'All')} className="hover:text-teal-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};
